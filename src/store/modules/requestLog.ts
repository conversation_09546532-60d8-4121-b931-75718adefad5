import { defineStore } from "pinia";
import { RequestLogApi } from "@/api/requestLog";
import type {
  RequestLogQueryRequest,
  OpenAiRequestLogResponse,
  RequestLogDetail,
  LogStatsData,
  ProviderStats,
  TimeRangeStats,
  ErrorTypeStats,
  ModelUsageStats,
  LogAnalysisResult,
  PageResult
} from "@/types/requestLog";

interface RequestLogState {
  // 日志数据
  logs: OpenAiRequestLogResponse[];
  currentLog: RequestLogDetail | null;

  // 分页信息
  pagination: {
    page: number;
    size: number;
    total: number;
    totalPages: number;
  };

  // 查询条件
  filters: RequestLogQueryRequest;

  // 统计数据
  stats: LogStatsData | null;
  providerStats: ProviderStats[];
  timeRangeStats: TimeRangeStats[];
  errorTypeStats: ErrorTypeStats[];
  modelUsageStats: ModelUsageStats[];
  analysisResult: LogAnalysisResult | null;

  // 加载状态
  loading: {
    logs: boolean;
    detail: boolean;
    stats: boolean;
    analysis: boolean;
    export: boolean;
  };

  // 错误信息
  error: string | null;

  // 缓存控制
  lastFetchTime: {
    logs: number;
    stats: number;
    analysis: number;
  };
}

export const useRequestLogStore = defineStore("requestLog", {
  state: (): RequestLogState => ({
    logs: [],
    currentLog: null,
    pagination: {
      page: 1,
      size: 20,
      total: 0,
      totalPages: 0
    },
    filters: {
      page: 1,
      size: 20
    },
    stats: null,
    providerStats: [],
    timeRangeStats: [],
    errorTypeStats: [],
    modelUsageStats: [],
    analysisResult: null,
    loading: {
      logs: false,
      detail: false,
      stats: false,
      analysis: false,
      export: false
    },
    error: null,
    lastFetchTime: {
      logs: 0,
      stats: 0,
      analysis: 0
    }
  }),

  getters: {
    // 获取成功的请求数量
    successLogsCount: (state) => {
      return state.logs.filter(log => log.status === "success").length;
    },

    // 获取失败的请求数量
    failedLogsCount: (state) => {
      return state.logs.filter(log => log.status === "failed").length;
    },

    // 计算成功率
    successRate: (state) => {
      if (state.logs.length === 0) return 0;
      const successCount = state.logs.filter(log => log.status === "success").length;
      return Math.round((successCount / state.logs.length) * 100);
    },

    // 计算平均响应时间
    avgResponseTime: (state) => {
      const validLogs = state.logs.filter(log => log.durationMs && log.durationMs > 0);
      if (validLogs.length === 0) return 0;
      const totalTime = validLogs.reduce((sum, log) => sum + (log.durationMs || 0), 0);
      return Math.round(totalTime / validLogs.length);
    },

    // 按提供商分组的日志
    logsByProvider: (state) => {
      return state.logs.reduce((acc, log) => {
        if (!acc[log.provider]) {
          acc[log.provider] = [];
        }
        acc[log.provider].push(log);
        return acc;
      }, {} as Record<string, OpenAiRequestLogResponse[]>);
    },

    // 获取慢请求（超过5秒）
    slowLogs: (state) => {
      return state.logs.filter(log => log.durationMs && log.durationMs > 5000);
    },

    // 获取重试次数较多的请求
    highRetryLogs: (state) => {
      return state.logs.filter(log => log.retryCount > 2);
    }
  },

  actions: {
    // 基础查询操作
    async fetchLogs(query?: Partial<RequestLogQueryRequest>) {
      this.loading.logs = true;
      this.error = null;

      try {
        const finalQuery = { ...this.filters, ...query };
        this.filters = finalQuery;

        const result = await RequestLogApi.getOpenaiRequestLogsPage(finalQuery);

        this.logs = result.data || [];
        this.pagination = {
          page: result.page || 1,
          size: result.size || 20,
          total: result.total || 0,
          totalPages: result.totalPages || 0
        };
        this.lastFetchTime.logs = Date.now();

        return result;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "获取日志失败";
        // 确保在错误时logs仍然是数组
        if (!Array.isArray(this.logs)) {
          this.logs = [];
        }
        throw error;
      } finally {
        this.loading.logs = false;
      }
    },

    async fetchFailedLogs(page = 1, size = 20, userId?: number) {
      this.loading.logs = true;
      this.error = null;

      try {
        const result = await RequestLogApi.getFailedOpenaiRequestLogs(page, size, userId);
        this.logs = result.data;
        this.pagination = {
          page: result.page,
          size: result.size,
          total: result.total,
          totalPages: result.totalPages
        };
        return result;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "获取失败日志失败";
        throw error;
      } finally {
        this.loading.logs = false;
      }
    },

    async fetchLogsByProvider(provider: string, page = 1, size = 20) {
      this.loading.logs = true;
      this.error = null;

      try {
        const result = await RequestLogApi.getOpenaiRequestLogsByProvider(provider, page, size);
        this.logs = result.data;
        this.pagination = {
          page: result.page,
          size: result.size,
          total: result.total,
          totalPages: result.totalPages
        };
        return result;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "获取提供商日志失败";
        throw error;
      } finally {
        this.loading.logs = false;
      }
    },

    async fetchLogDetail(requestId: string) {
      this.loading.detail = true;
      this.error = null;

      try {
        this.currentLog = await RequestLogApi.getRequestLogDetail(requestId);
        return this.currentLog;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "获取日志详情失败";
        throw error;
      } finally {
        this.loading.detail = false;
      }
    },

    // 删除操作
    async deleteLog(id: number) {
      this.loading.logs = true;
      this.error = null;

      try {
        await RequestLogApi.deleteOpenaiRequestLog(id);
        this.logs = this.logs.filter(log => log.id !== id);
        this.pagination.total -= 1;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "删除日志失败";
        throw error;
      } finally {
        this.loading.logs = false;
      }
    },

    // 统计数据
    async fetchStats(startTime?: string, endTime?: string, provider?: string) {
      this.loading.stats = true;
      this.error = null;

      try {
        this.stats = await RequestLogApi.getLogStats(startTime, endTime, provider);
        this.lastFetchTime.stats = Date.now();
        return this.stats;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "获取统计数据失败";
        throw error;
      } finally {
        this.loading.stats = false;
      }
    },

    async fetchProviderStats(startTime?: string, endTime?: string) {
      this.loading.stats = true;
      this.error = null;

      try {
        this.providerStats = await RequestLogApi.getProviderStats(startTime, endTime);
        return this.providerStats;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "获取提供商统计失败";
        throw error;
      } finally {
        this.loading.stats = false;
      }
    },

    // 更新过滤条件
    updateFilters(newFilters: Partial<RequestLogQueryRequest>) {
      this.filters = { ...this.filters, ...newFilters };
    },

    // 重置过滤条件
    resetFilters() {
      this.filters = {
        page: 1,
        size: 20
      };
    },

    // 清除错误
    clearError() {
      this.error = null;
    },

    // 重置状态
    resetState() {
      this.$reset();
    }
  }
});
