import { defineStore } from "pinia";
import { AiConfigApi } from "@/api/aiConfig";
import type {
  UserConfigGroupDto,
  ApiKeyDto,
  BatchAddApiKeyRequest,
  BatchAddApiKeyResult,
  CompatibleApiKeyDto,
  CreateCompatibleKeyRequest,
  ApiKeyTestResult,
  ApiKeyUpdateRequest,
  ApiKeyStatsDto
} from "@/types/aiConfig";

interface AiConfigState {
  // 配置分组相关
  configGroups: UserConfigGroupDto[];
  currentGroup: UserConfigGroupDto | null;
  
  // API密钥相关
  apiKeys: ApiKeyDto[];
  currentApiKey: ApiKeyDto | null;
  
  // 兼容密钥相关
  compatibleKeys: CompatibleApiKeyDto[];
  
  // 统计数据
  keyStats: ApiKeyStatsDto | null;
  
  // 加载状态
  loading: {
    groups: boolean;
    keys: boolean;
    compatibleKeys: boolean;
    stats: boolean;
    testing: boolean;
  };
  
  // 错误信息
  error: string | null;
  
  // 缓存控制
  lastFetchTime: {
    groups: number;
    keys: number;
    compatibleKeys: number;
    stats: number;
  };
}

export const useAiConfigStore = defineStore("aiConfig", {
  state: (): AiConfigState => ({
    configGroups: [],
    currentGroup: null,
    apiKeys: [],
    currentApiKey: null,
    compatibleKeys: [],
    keyStats: null,
    loading: {
      groups: false,
      keys: false,
      compatibleKeys: false,
      stats: false,
      testing: false
    },
    error: null,
    lastFetchTime: {
      groups: 0,
      keys: 0,
      compatibleKeys: 0,
      stats: 0
    }
  }),

  getters: {
    // 获取当前分组的API密钥数量
    currentGroupApiKeyCount: (state) => {
      return state.currentGroup ? state.apiKeys.length : 0;
    },

    // 获取活跃的API密钥数量
    activeApiKeyCount: (state) => {
      return state.apiKeys.filter(key => key.isActive).length;
    },

    // 获取失效的API密钥数量
    inactiveApiKeyCount: (state) => {
      return state.apiKeys.filter(key => !key.isActive).length;
    },

    // 按提供商分组的配置分组
    groupsByProvider: (state) => {
      return state.configGroups.reduce((acc, group) => {
        if (!acc[group.provider]) {
          acc[group.provider] = [];
        }
        acc[group.provider].push(group);
        return acc;
      }, {} as Record<string, UserConfigGroupDto[]>);
    },

    // 获取所有提供商列表
    allProviders: (state) => {
      return [...new Set(state.configGroups.map(group => group.provider))];
    },

    // 获取当前分组的活跃密钥
    currentGroupActiveKeys: (state) => {
      return state.apiKeys.filter(key => key.isActive);
    },

    // 检查是否有数据需要刷新（5分钟缓存）
    needsRefresh: (state) => {
      const now = Date.now();
      const cacheTime = 5 * 60 * 1000; // 5分钟
      return {
        groups: now - state.lastFetchTime.groups > cacheTime,
        keys: now - state.lastFetchTime.keys > cacheTime,
        compatibleKeys: now - state.lastFetchTime.compatibleKeys > cacheTime,
        stats: now - state.lastFetchTime.stats > cacheTime
      };
    }
  },

  actions: {
    // 配置分组管理
    async fetchConfigGroups(force = false) {
      if (!force && !this.needsRefresh.groups && this.configGroups.length > 0) {
        return this.configGroups;
      }

      this.loading.groups = true;
      this.error = null;
      
      try {
        this.configGroups = await AiConfigApi.getUserConfigGroups();
        this.lastFetchTime.groups = Date.now();
        return this.configGroups;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "获取配置分组失败";
        throw error;
      } finally {
        this.loading.groups = false;
      }
    },

    async createConfigGroup(config: UserConfigGroupDto) {
      this.loading.groups = true;
      this.error = null;
      
      try {
        const newGroup = await AiConfigApi.createOrUpdateUserConfigGroup(config);
        this.configGroups.push(newGroup);
        this.lastFetchTime.groups = Date.now();
        return newGroup;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "创建配置分组失败";
        throw error;
      } finally {
        this.loading.groups = false;
      }
    },

    async updateConfigGroup(config: UserConfigGroupDto) {
      this.loading.groups = true;
      this.error = null;
      
      try {
        const updatedGroup = await AiConfigApi.createOrUpdateUserConfigGroup(config);
        const index = this.configGroups.findIndex(g => g.id === updatedGroup.id);
        if (index !== -1) {
          this.configGroups[index] = updatedGroup;
        }
        if (this.currentGroup?.id === updatedGroup.id) {
          this.currentGroup = updatedGroup;
        }
        this.lastFetchTime.groups = Date.now();
        return updatedGroup;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "更新配置分组失败";
        throw error;
      } finally {
        this.loading.groups = false;
      }
    },

    async deleteConfigGroup(groupId: number) {
      this.loading.groups = true;
      this.error = null;
      
      try {
        await AiConfigApi.deleteUserConfigGroup(groupId);
        this.configGroups = this.configGroups.filter(g => g.id !== groupId);
        if (this.currentGroup?.id === groupId) {
          this.currentGroup = null;
          this.apiKeys = [];
        }
        this.lastFetchTime.groups = Date.now();
      } catch (error) {
        this.error = error instanceof Error ? error.message : "删除配置分组失败";
        throw error;
      } finally {
        this.loading.groups = false;
      }
    },

    // 设置当前分组
    async setCurrentGroup(group: UserConfigGroupDto | null) {
      this.currentGroup = group;
      if (group) {
        await this.fetchApiKeys(group.id!);
      } else {
        this.apiKeys = [];
      }
    },

    // API密钥管理
    async fetchApiKeys(configGroupId: number, force = false) {
      if (!force && !this.needsRefresh.keys && this.apiKeys.length > 0) {
        return this.apiKeys;
      }

      this.loading.keys = true;
      this.error = null;
      
      try {
        this.apiKeys = await AiConfigApi.getUserApiKeysByConfigGroupId(configGroupId);
        this.lastFetchTime.keys = Date.now();
        return this.apiKeys;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "获取API密钥失败";
        throw error;
      } finally {
        this.loading.keys = false;
      }
    },

    async batchAddApiKeys(request: BatchAddApiKeyRequest) {
      this.loading.keys = true;
      this.error = null;
      
      try {
        const result = await AiConfigApi.batchAddApiKeys(request);
        // 刷新当前分组的API密钥列表
        if (this.currentGroup?.id === request.configGroupId) {
          await this.fetchApiKeys(request.configGroupId, true);
        }
        return result;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "批量添加API密钥失败";
        throw error;
      } finally {
        this.loading.keys = false;
      }
    },

    async testApiKey(keyId: number): Promise<ApiKeyTestResult> {
      this.loading.testing = true;
      this.error = null;
      
      try {
        const result = await AiConfigApi.testApiKey(keyId);
        // 更新本地密钥状态
        const keyIndex = this.apiKeys.findIndex(key => key.id === keyId);
        if (keyIndex !== -1) {
          this.apiKeys[keyIndex].isActive = result.valid;
        }
        return result;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "测试API密钥失败";
        throw error;
      } finally {
        this.loading.testing = false;
      }
    },

    async updateApiKey(keyId: number, updateData: ApiKeyUpdateRequest) {
      this.loading.keys = true;
      this.error = null;
      
      try {
        const result = await AiConfigApi.updateApiKey(keyId, updateData);
        // 更新本地状态中的密钥信息
        const index = this.apiKeys.findIndex(key => key.id === keyId);
        if (index !== -1) {
          this.apiKeys[index] = result;
        }
        this.lastFetchTime.keys = Date.now();
        return result;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "更新API密钥失败";
        throw error;
      } finally {
        this.loading.keys = false;
      }
    },

    async deleteApiKey(keyId: number) {
      this.loading.keys = true;
      this.error = null;
      
      try {
        await AiConfigApi.deleteApiKey(keyId);
        this.apiKeys = this.apiKeys.filter(key => key.id !== keyId);
        this.lastFetchTime.keys = Date.now();
      } catch (error) {
        this.error = error instanceof Error ? error.message : "删除API密钥失败";
        throw error;
      } finally {
        this.loading.keys = false;
      }
    },

    // 兼容密钥管理
    async fetchCompatibleKeys(force = false) {
      if (!force && !this.needsRefresh.compatibleKeys && this.compatibleKeys.length > 0) {
        return this.compatibleKeys;
      }

      this.loading.compatibleKeys = true;
      this.error = null;
      
      try {
        this.compatibleKeys = await AiConfigApi.getCompatibleKeys();
        this.lastFetchTime.compatibleKeys = Date.now();
        return this.compatibleKeys;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "获取兼容密钥失败";
        throw error;
      } finally {
        this.loading.compatibleKeys = false;
      }
    },

    async generateCompatibleKey(request: CreateCompatibleKeyRequest) {
      this.loading.compatibleKeys = true;
      this.error = null;
      
      try {
        const result = await AiConfigApi.generateCompatibleKey(request);
        this.compatibleKeys.push(result);
        this.lastFetchTime.compatibleKeys = Date.now();
        return result;
      } catch (error) {
        this.error = error instanceof Error ? error.message : "生成兼容密钥失败";
        throw error;
      } finally {
        this.loading.compatibleKeys = false;
      }
    },

    // 清除错误
    clearError() {
      this.error = null;
    },

    // 重置状态
    resetState() {
      this.$reset();
    }
  }
});
